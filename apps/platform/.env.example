# GENERAL
NODE_ENV=<development|staging|production>
APP_TAG=thena-platform
SERVICE_TAG=thena-platform
BASE_URL=<base-url> #http://localhost:8000
SENTRY_DSN=<sentry-dsn>


# JWT
JWT_SECRET=<jwt-secret>

# AWS CREDENTIALS
AWS_ACCESS_KEY=<access-key>
AWS_SECRET_KEY=<secret-key>
AWS_REGION=<region>

# AWS SNS TOPICS
AWS_SNS_TICKET_TOPIC_ARN=<arn>
AWS_SNS_ORGANIZATION_TOPIC_ARN=<arn>
AWS_SNS_ACCOUNTS_TOPIC_ARN=<arn>
AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN=<arn>


# AWS SQS QUEUE
AWS_SQS_NOTIFICATIONS_QUEUE_URL=<sqs-queue-url>

# CLIENT GRPC URL
AUTH_GRPC_URL=<auth-grpc-url> #0.0.0.0:50052
WORKFLOWS_GRPC_URL=<workflows-grpc-url> #localhost:50053
PLATFORM_GRPC_URL=<platform-grpc-url> #localhost:50051

# Redis - local
REDIS_HOST=<redis-host> #localhost
REDIS_PORT=<redis-port> #6379
REDIS_USERNAME=<redis-username> #default
REDIS_PASSWORD=<redis-password> #default

# DB - local
THENA_PLATFORM_DB_HOST=<db-host> #localhost
THENA_PLATFORM_DB_PORT=<db-port> #54322
THENA_PLATFORM_DB_NAME=<db-name> #postgres
THENA_PLATFORM_DB_USER=<db-user> #postgres
THENA_PLATFORM_DB_PASSWORD=<db-password> #postgres

# STORAGE - SUPABADSE
SUPABASE_BUCKET=<supabase-bucket> # thena-platform
SUPABASE_URL=<supabase-url> # http://localhost:54321
SUPABASE_KEY=<supabase-key> # use supabase status to get this. 

#KNOCK
KNOCK_API_KEY=<knock-api-key>
KNOCK_SLACK_CHANNEL_ID=<knock-slack-channel-id>

# Bull Board
BULL_BOARD_ADMIN_USER=<bull-board-admin-user>
BULL_BOARD_ADMIN_PASSWORD=<bull-board-admin-password>

# TypeSense
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=abc
TYPESENSE_ADMIN_API_KEY=xyz
TYPESENSE_TIMEOUT=10000


# This key must be a Typesense search-only API key (with only 'documents:search' permission).
# Do NOT use the global admin/master key here. This is critical for security.
TYPESENSE_SEARCH_ONLY_API_KEY=abc

# VAULT
VAULT_URL=<vault-url>
VAULT_TOKEN=<vault-token>
CERT_PATH=<vault-cert-path>

#Swagger
GENERATE_SWAGGER=true